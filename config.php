<?php
// config.php - Database connection and helper functions

$servername = "127.0.0.1";
$username = "root"; // Default XAMPP username
$password = "";     // Default XAMPP password (empty)
$dbname = "sistem_crm";

// Create connection
$conn = mysqli_connect($servername, $username, $password, $dbname);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Set character set
mysqli_set_charset($conn, "utf8mb4");

// Create activity_logs table if it doesn't exist (for tracking admin actions)
try {
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Silently fail - not critical for system operation
}

// Function to log admin activity
function logActivity($userId, $action, $details) {
    global $pdo;
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)");
    $stmt->execute([$userId, $action, $details, $ip]);
}

// Authentication functions
function authenticateUser($username, $password) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        return $user;
    }
    return false;
}

// Agent functions
function getAllAgents() {
    global $pdo;
    $agents = array();
    
    $sql = "SELECT * FROM agents ORDER BY name ASC";
    $result = $pdo->query($sql);
    
    if ($result && $result->rowCount() > 0) {
        while ($row = $result->fetch()) {
            $agents[] = $row;
        }
    }
    
    return $agents;
}

function getAgentById($agentId) {
    // Mock data lookup
    $agents = getAllAgents();
    foreach ($agents as $agent) {
        if ($agent['agent_id'] == $agentId) {
            return $agent;
        }
    }
    return false;
}

function createAgent($data) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO agents (name, ic_number, gender, date_of_birth, phone_number, email, address, 
                          beneficiary_phone, work_experience) 
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $data['name'], 
        $data['ic_number'], 
        $data['gender'], 
        $data['date_of_birth'], 
        $data['phone_number'], 
        $data['email'], 
        $data['address'],
        $data['beneficiary_phone'] ?? null, 
        $data['work_experience'] ?? null
    ]);
    
    return $pdo->lastInsertId();
}

/**
 * Update agent details in the database
 * 
 * @param string $oldAgentId Original agent ID
 * @param array $data Agent data to update
 * @return bool Success status
 */
function updateAgent($oldAgentId, $data) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Check if the agent ID has changed
        $newAgentId = $data['agent_id'];
        $idChanged = ($oldAgentId !== $newAgentId);
        
        // Build the update query
        $sql = "UPDATE agents SET 
                name = :name,
                ic_number = :ic_number,
                gender = :gender,
                date_of_birth = :date_of_birth,
                phone_number = :phone_number,
                email = :email,
                address = :address,
                beneficiary_phone = :beneficiary_phone,
                work_experience = :work_experience";
        
        // Add photo to update if it's provided
        if (isset($data['photo'])) {
            $sql .= ", photo = :photo";
        }
        
        // If ID changed, update that too
        if ($idChanged) {
            $sql .= ", agent_id = :new_agent_id";
        }
        
        $sql .= " WHERE agent_id = :old_agent_id";
        
        $stmt = $pdo->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':ic_number', $data['ic_number']);
        $stmt->bindParam(':gender', $data['gender']);
        $stmt->bindParam(':date_of_birth', $data['date_of_birth']);
        $stmt->bindParam(':phone_number', $data['phone_number']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':beneficiary_phone', $data['beneficiary_phone']);
        $stmt->bindParam(':work_experience', $data['work_experience']);
        $stmt->bindParam(':old_agent_id', $oldAgentId);
        
        if (isset($data['photo'])) {
            $stmt->bindParam(':photo', $data['photo']);
        }
        
        if ($idChanged) {
            $stmt->bindParam(':new_agent_id', $newAgentId);
        }
        
        $result = $stmt->execute();
        
        if (!$result) {
            $errorInfo = $stmt->errorInfo();
            error_log("SQL execution failed: " . print_r($errorInfo, true));
            throw new Exception("SQL execution failed: " . $errorInfo[2]);
        }
        
        $rowCount = $stmt->rowCount();
        
        // If the agent ID changed, update related tables
        if ($idChanged && $result) {
            // Update agent_documents table
            $stmt = $pdo->prepare("UPDATE agent_documents SET agent_id = :new_agent_id WHERE agent_id = :old_agent_id");
            $stmt->bindParam(':new_agent_id', $newAgentId);
            $stmt->bindParam(':old_agent_id', $oldAgentId);
            $stmt->execute();
            
            // Log the ID change
            if (isset($_SESSION['user_id'])) {
                logActivity($_SESSION['user_id'], "update_agent", "Updated agent. ID changed from {$oldAgentId} to {$newAgentId}");
            }
        } else if ($result) {
            if (isset($_SESSION['user_id'])) {
                logActivity($_SESSION['user_id'], "update_agent", "Updated agent {$oldAgentId}");
            }
        }
        
        $pdo->commit();
        return true; // Return true if SQL executed successfully, regardless of row count
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Database error in updateAgent: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("General error in updateAgent: " . $e->getMessage());
        return false;
    }
}

function deleteAgent($agentId) {
    global $conn;
    
    try {
        // Start transaction
        mysqli_begin_transaction($conn);
        
        // 1. Delete from policies
        $query = "DELETE FROM policies WHERE agent_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $agentId);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
        
        // 2. Delete from agent_documents
        $query = "DELETE FROM agent_documents WHERE agent_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $agentId);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
        
        // 3. Delete from educationdetails
        $query = "DELETE FROM educationdetails WHERE agent_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $agentId);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
        
        // 4. Finally delete from agents table
        $query = "DELETE FROM agents WHERE agent_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $agentId);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
        
        // Commit transaction
        mysqli_commit($conn);
        
        return true;
    } catch (Exception $e) {
        // Rollback on error
        mysqli_rollback($conn);
        error_log("Error deleting agent: " . $e->getMessage());
        return false;
    }
}

// Client functions
function getAllClients() {
    global $conn;
    $clients = [];
    
    $query = "SELECT * FROM clients";
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        die("Database query failed: " . mysqli_error($conn));
    }
    
    while ($row = mysqli_fetch_assoc($result)) {
        $clients[] = $row;
    }
    
    return $clients;
}

/**
 * Get a client by ID
 */
function getClientById($id) {
    global $conn;
    
    // Sanitize the input
    $id = mysqli_real_escape_string($conn, $id);
    
    // Make sure you're using the correct column name
    $query = "SELECT * FROM clients WHERE client_id = '$id'";
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        throw new Exception("Database query error: " . mysqli_error($conn));
    }
    
    return mysqli_fetch_assoc($result);
}

// Agent-Client relationship functions
function getAgentClients($agentId) {
    global $conn;
    
    $query = "SELECT c.client_id, c.name, c.client_number, 
                   p.policy_id as policy_number 
            FROM clients c
            LEFT JOIN policies p ON c.client_id = p.client_id
            WHERE p.agent_id = '$agentId'";
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        die("Database query failed: " . mysqli_error($conn));
    }
    
    $clients = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $clients[] = $row;
    }
    
    return $clients;
}

// Education details functions
function getAgentEducation($agentId) {
    global $conn;
    $query = "SELECT * FROM educationdetails WHERE agent_id = ? ORDER BY year_completed DESC";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $agentId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (!$result) {
        die("Database query failed: " . mysqli_error($conn));
    }
    
    $education = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $education[] = $row;
    }
    
    mysqli_stmt_close($stmt);
    return $education;
}

// Document functions
function getAgentDocuments($agentId) {
    global $conn;
    $query = "SELECT ad.*, dt.type_name 
              FROM agent_documents ad 
              JOIN document_types dt ON ad.document_type_id = dt.document_type_id 
              WHERE ad.agent_id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $agentId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (!$result) {
        die("Database query failed: " . mysqli_error($conn));
    }
    
    $documents = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $documents[$row['type_name']] = $row;
    }
    
    mysqli_stmt_close($stmt);
    return $documents;
}

/**
 * Check if an agent ID already exists in the database
 * 
 * @param string $agentId Agent ID to check
 * @return bool True if exists, false otherwise
 */
function agentIdExists($agentId) {
    global $conn;
    
    $query = "SELECT COUNT(*) FROM agents WHERE agent_id = $agentId";
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        die("Database query failed: " . mysqli_error($conn));
    }
    
    return mysqli_fetch_row($result)[0] > 0;
}

/**
 * Add a new agent with optional education details
 * @param array $data The agent data
 * @param array $educationData Optional education data
 * @return bool True on success, false on failure
 */
function addAgent($data, $educationData = []) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "INSERT INTO agents (agent_id, name, ic_number, gender, date_of_birth, 
                              phone_number, email, address, beneficiary_phone, beneficiary_name, 
                              beneficiary_ic, work_experience, photo) 
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    mysqli_stmt_bind_param($stmt, "ssssssssssssss",
        $data['agent_id'],
        $data['name'], 
        $data['ic_number'], 
        $data['gender'], 
        $data['date_of_birth'], 
        $data['phone_number'], 
        $data['email'], 
        $data['address'],
        $data['beneficiary_phone'] ?? null,
        $data['beneficiary_name'] ?? null,
        $data['beneficiary_ic'] ?? null,
        $data['work_experience'] ?? null,
        $data['photo'] ?? null
    );
    
    mysqli_stmt_execute($stmt);
    
    $agentId = mysqli_insert_id($conn);
    
    // Insert education details if any
    if (!empty($educationData)) {
        foreach ($educationData as $edu) {
            if (!empty($edu['level'])) {
                $stmt = mysqli_prepare($conn, "INSERT INTO educationdetails (agent_id, level, year_completed, institution_name) 
                                             VALUES (?, ?, ?, ?)");
                mysqli_stmt_bind_param($stmt, "ssss", $agentId, $edu['level'], $edu['year'] ?? null, $edu['institution'] ?? null);
                mysqli_stmt_execute($stmt);
            }
        }
    }
    
    return $agentId;
}

/**
 * Handle document uploads for an agent
 * @param string $agentId The agent ID
 * @return bool True on success, false on failure
 */
function handleDocumentUploads($agentId) {
    global $conn;
    
    if (!isset($_FILES['documents']) || empty($_FILES['documents'])) {
        return;
    }
    
    $uploadDir = 'uploads/documents/';
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0777, true)) {
            throw new Exception('Failed to create upload directory for documents');
        }
    }
    
    foreach ($_FILES['documents']['name'] as $type => $filename) {
        if (empty($filename)) continue;
        
        $tempFile = $_FILES['documents']['tmp_name'][$type];
        $errorCode = $_FILES['documents']['error'][$type];
        
        if ($errorCode === UPLOAD_ERR_OK) {
            // Get document type ID based on the type name
            $docTypeId = getDocumentTypeId($type);
            if (!$docTypeId) continue;
            
            // Generate unique filename
            $fileExtension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $newFileName = $agentId . '_' . $type . '_' . time() . '_' . $filename;
            $targetPath = $uploadDir . $newFileName;
            
            // Move the file
            if (move_uploaded_file($tempFile, $targetPath)) {
                $fileSize = filesize($targetPath);
                
                // Insert into database
                $stmt = mysqli_prepare($conn, "INSERT INTO agent_documents (agent_id, document_type_id, file_name, file_path, file_size) 
                                        VALUES (?, ?, ?, ?, ?)");
                mysqli_stmt_bind_param($stmt, "isiss", $agentId, $docTypeId, $filename, $targetPath, $fileSize);
                mysqli_stmt_execute($stmt);
                mysqli_stmt_close($stmt);
            }
        }
    }
}

/**
 * Get document type ID based on name
 * @param string $typeName The type name of the document
 * @return int|null The document type ID, or null if not found
 */
function getDocumentTypeId($typeName) {
    global $conn;
    
    // Map document type from form to database ID
    $typeMap = [
        'education_cert' => 1,  // education certificate
        'nric' => 2,            // NRIC
        'beneficiary_nric' => 3 // Beneficiary NRIC
    ];
    
    return $typeMap[$typeName] ?? null;
}

/**
 * Ensure that the document types exist in the database
 */
function ensureDocumentTypesExist() {
    global $conn;
    
    // Check if any document types exist
    $query = "SELECT COUNT(*) FROM document_types";
    $result = mysqli_query($conn, $query);
    $count = mysqli_fetch_row($result)[0];
    
    if ($count == 0) {
        // Insert default document types
        $defaultTypes = [
            ['Education Certificate', 'Education certificates and qualifications', true],
            ['NRIC', 'National Registration Identity Card', true],
            ['Beneficiary NRIC', 'Beneficiary National Registration Identity Card', false],
            ['Passport', 'Passport photo or document', false],
            ['Other Document', 'Other supporting documents', false]
        ];
        
        foreach ($defaultTypes as $index => $type) {
            $stmt = mysqli_prepare($conn, "INSERT INTO document_types (document_type_id, type_name, description, required) 
                                          VALUES (?, ?, ?, ?)");
            mysqli_stmt_bind_param($stmt, "isib", $index + 1, $type[0], $type[1], $type[2]);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
    }
}

/**
 * Get all agents with sorting
 * @param string $sortColumn Column to sort by
 * @param string $sortOrder Sort direction (asc/desc)
 * @return array Array of agents
 */
function getAllAgentsSorted($sortColumn = 'agent_id', $sortOrder = 'asc') {
    global $conn;
    
    // Validate sort order
    $sortOrder = strtolower($sortOrder) === 'desc' ? 'DESC' : 'ASC';
    
    // Use prepared statement to prevent SQL injection
    $query = "SELECT * FROM agents ORDER BY " . mysqli_real_escape_string($conn, $sortColumn) . " " . mysqli_real_escape_string($conn, $sortOrder);
    
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        die("Database query failed: " . mysqli_error($conn));
    }
    
    $agents = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $agents[] = $row;
    }
    
    return $agents;
}

/**
 * Add a new client to the database
 * 
 * @param array $clientData The client data to insert
 * @return bool True if client was added successfully, false otherwise
 */
function addClient($clientData) {
    global $conn;
    
    try {
        // Start transaction
        mysqli_begin_transaction($conn);
        
        // Insert into clients table
        $query = "INSERT INTO clients (
            client_id, name, ic_number, client_number, gender, date_of_birth,
            phone_number, email, address, marital_status, race, religion,
            nationality, occupation, exact_duties, nature_of_business,
            salary_yearly, company_name, company_address, weight, height,
            smoker, hospital_admission_history
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "sssssssssssssssssssssss",
            $clientData['client_id'],
            $clientData['name'],
            $clientData['ic_number'],
            $clientData['client_number'],
            $clientData['gender'],
            $clientData['date_of_birth'],
            $clientData['phone_number'],
            $clientData['email'],
            $clientData['address'],
            $clientData['marital_status'],
            $clientData['race'],
            $clientData['religion'],
            $clientData['nationality'],
            $clientData['occupation'],
            $clientData['exact_duties'],
            $clientData['nature_of_business'],
            $clientData['salary_yearly'],
            $clientData['company_name'],
            $clientData['company_address'],
            $clientData['weight'],
            $clientData['height'],
            $clientData['smoker'],
            $clientData['hospital_admission_history']
        );
        
        mysqli_stmt_execute($stmt);
        
        // If agent_id is provided, create the relationship
        if (!empty($clientData['agent_id'])) {
            $query = "INSERT INTO agent_client (agent_id, client_id) VALUES (?, ?)";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "ss", $clientData['agent_id'], $clientData['client_id']);
            mysqli_stmt_execute($stmt);
        }
        
        // Commit transaction
        mysqli_commit($conn);
        
        return true;
    } catch (Exception $e) {
        // Rollback on error
        mysqli_rollback($conn);
        error_log("Error adding client: " . $e->getMessage());
        return false;
    }
}

/**
 * Upload a client document
 * 
 * @param string $clientId The client ID
 * @param string $documentType The type of document (ic, signature, bank_card)
 * @param array $file The uploaded file data
 * @return bool|string Returns the file path on success, false on failure
 */
function uploadClientDocument($clientId, $documentType, $file) {
    try {
        // Create upload directory if it doesn't exist
        $uploadDir = 'uploads/client_documents/' . $clientId;
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        // Generate unique filename
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $fileName = $documentType . '_' . time() . '.' . $fileExtension;
        $filePath = $uploadDir . '/' . $fileName;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return $filePath;
        }
        return false;
    } catch (Exception $e) {
        error_log("Error uploading client document: " . $e->getMessage());
        return false;
    }
}

/**
 * Save client document information to database
 * 
 * @param string $clientId The client ID
 * @param string $documentType The type of document
 * @param string $filePath The path to the uploaded file
 * @return bool True if successful, false otherwise
 */
function saveClientDocument($clientId, $documentType, $filePath) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "INSERT INTO client_documents (client_id, document_type, file_path, upload_date) VALUES (?, ?, ?, NOW())");
    mysqli_stmt_bind_param($stmt, "sss", $clientId, $documentType, $filePath);
    
    mysqli_stmt_execute($stmt);
    
    return mysqli_stmt_affected_rows($stmt) > 0;
}

function clientIdExists($client_id) {
    global $conn;
    
    // Use prepared statement to prevent SQL injection
    $query = "SELECT COUNT(*) FROM clients WHERE client_id = ?";
    $stmt = mysqli_prepare($conn, $query);
    
    if (!$stmt) {
        error_log("Error preparing statement: " . mysqli_error($conn));
        return false;
    }
    
    mysqli_stmt_bind_param($stmt, "s", $client_id);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_bind_result($stmt, $count);
    mysqli_stmt_fetch($stmt);
    mysqli_stmt_close($stmt);
    
    return $count > 0;
}

function getClientPolicies($client_id) {
    global $pdo;
    
    try {
        // Log the query being executed
        $query = "SELECT p.*, a.name as agent_name 
                  FROM policies p 
                  LEFT JOIN agents a ON p.agent_id = a.agent_id 
                  WHERE p.client_id = ?
                  ORDER BY p.created_at DESC";
        
        error_log("Running query: " . $query . " with client_id: " . $client_id);
        
        $stmt = $pdo->prepare($query);
        $stmt->execute([$client_id]);
        $policies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Debug the results
        foreach ($policies as $policy) {
            error_log("Policy: " . $policy['policy_id'] . ", Agent ID: " . $policy['agent_id'] . ", Agent Name: " . ($policy['agent_name'] ?? 'NULL'));
        }
        
        return $policies;
    } catch (Exception $e) {
        error_log("Error fetching client policies: " . $e->getMessage());
        return [];
    }
}

function deleteClient($clientId) {
    global $conn;
    
    try {
        // First verify client exists
        $query = "SELECT name FROM clients WHERE client_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "s", $clientId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $client = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$client) {
            throw new Exception("Client not found");
        }
        
        // Start transaction
        mysqli_begin_transaction($conn);
        
        // 1. Delete from policies first
        $query = "DELETE FROM policies WHERE client_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $clientId);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
        
        // 2. Check if client_documents table exists before trying to delete
        $tableCheck = mysqli_query($conn, "SHOW TABLES LIKE 'client_documents'");
        if (mysqli_num_rows($tableCheck) > 0) {
            $query = "DELETE FROM client_documents WHERE client_id = ?";
            $stmt = mysqli_prepare($conn, $query);
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, "s", $clientId);
                mysqli_stmt_execute($stmt);
                mysqli_stmt_close($stmt);
            }
        }
        
        // 3. Finally delete from clients table
        $query = "DELETE FROM clients WHERE client_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $clientId);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
        
        // Commit transaction
        mysqli_commit($conn);
        
        return true;
    } catch (Exception $e) {
        // Rollback on error
        if (mysqli_get_transaction_status($conn)) {
            mysqli_rollback($conn);
        }
        error_log("Error deleting client: " . $e->getMessage());
        return false;
    }
}
?>
